{"name": "@braintrust/openai-agents", "version": "0.0.1", "description": "SDK for integrating Braintrust with OpenAI Agents", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "module": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"build": "tsup", "watch": "tsup --watch", "clean": "rm -r dist/*", "test": "vitest run"}, "author": "Braintrust Data Inc.", "license": "MIT", "devDependencies": {"@openai/agents": "^0.0.15", "@types/node": "^20.10.5", "tsup": "^8.3.5", "tsx": "^3.14.0", "typescript": "^5.3.3", "vitest": "^2.1.9", "zod": "^3.25.34"}, "dependencies": {"@braintrust/core": "workspace:*", "braintrust": "workspace:^"}, "peerDependencies": {"@openai/agents": "^0.0.14", "@openai/agents-core": "^0.0.14"}}